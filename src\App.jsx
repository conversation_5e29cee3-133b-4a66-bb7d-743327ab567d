// App.jsx
import { useState } from "react";

export default function App() {
  const [query, setQuery] = useState("");
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [page, setPage] = useState(1); // pagination

  const searchBooks = async (e, newSearch = true) => {
    if (e) e.preventDefault();
    if (!query) return;

    setLoading(true);
    setError("");

    try {
      const res = await fetch(
        `https://openlibrary.org/search.json?title=${query}&page=${page}`
      );
      if (!res.ok) throw new Error("Failed to fetch");
      const data = await res.json();

      if (data.docs.length === 0) {
        setError("No books found. Try another title.");
      } else {
        if (newSearch) {
          setBooks(data.docs); // fresh search
        } else {
          setBooks((prev) => [...prev, ...data.docs]); // load more
        }
      }
    } catch (err) {
      setError("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleNewSearch = (e) => {
    setPage(1);
    setBooks([]);
    searchBooks(e, true);
  };

  const loadMore = () => {
    setPage((prev) => prev + 1);
    searchBooks(null, false);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <h1 className="text-3xl font-bold text-center mb-6">📚 Book Finder</h1>

      <form
        onSubmit={handleNewSearch}
        className="flex justify-center mb-6 gap-2"
      >
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search by book title..."
          className="px-4 py-2 rounded-lg border w-72"
        />
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Search
        </button>
      </form>

      {loading && (
        <div className="flex justify-center items-center my-6">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {error && <p className="text-center text-red-500">{error}</p>}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {books.map((book, index) => {
          const coverId = book.cover_i;
          const coverUrl = coverId
            ? `https://covers.openlibrary.org/b/id/${coverId}-M.jpg`
            : "https://via.placeholder.com/150x200?text=No+Cover";

          return (
            <div
              key={index}
              className="bg-white rounded-xl shadow-md overflow-hidden"
            >
              <img
                src={coverUrl}
                alt={book.title}
                className="w-full h-60 object-cover"
              />
              <div className="p-4">
                <h2 className="text-lg font-semibold">{book.title}</h2>
                <p className="text-gray-600 text-sm">
                  {book.author_name ? book.author_name.join(", ") : "Unknown"}
                </p>
                <p className="text-gray-500 text-xs mt-2">
                  First published: {book.first_publish_year || "N/A"}
                </p>
              </div>
            </div>
          );
        })}
      </div>

      {books.length > 0 && !loading && (
        <div className="flex justify-center mt-6">
          <button
            onClick={loadMore}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  );
}
